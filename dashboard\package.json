{"name": "modubot-dashboard", "version": "1.0.0", "description": "Professional web dashboard for ModuBot Discord server management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "passport": "^0.6.0", "passport-discord": "^0.1.4", "sqlite3": "^5.1.6", "ejs": "^3.1.9", "axios": "^1.6.0", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "moment": "^2.29.4", "chart.js": "^4.4.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": "18.x"}, "repository": {"type": "git", "url": "https://github.com/your-username/modubot-dashboard.git"}, "keywords": ["discord", "bot", "dashboard", "moderation", "server-management"], "author": "ModuBot Team", "license": "MIT"}