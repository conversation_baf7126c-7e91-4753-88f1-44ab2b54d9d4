# ModuBot - Complete Discord Server Setup Guide

## Table of Contents
1. [Bot Setup & Installation](#bot-setup--installation)
2. [Discord Server Creation & Optimization](#discord-server-creation--optimization)
3. [Permission System & Role Hierarchy](#permission-system--role-hierarchy)
4. [Channel Structure & Organization](#channel-structure--organization)
5. [ModuBot Configuration](#modubot-configuration)
6. [Complementary Bots & Integrations](#complementary-bots--integrations)
7. [Server Customization & Branding](#server-customization--branding)
8. [Moderation Best Practices](#moderation-best-practices)
9. [Community Engagement Strategies](#community-engagement-strategies)
10. [Troubleshooting & Maintenance](#troubleshooting--maintenance)

---

## Bot Setup & Installation

### Prerequisites
- Node.js 16.0.0 or higher
- A Discord account with Developer Portal access
- Basic command line knowledge

### Step 1: Create Discord Application
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and name it "ModuBot"
3. Navigate to the "Bot" section
4. Click "Add Bot" and confirm
5. Copy the bot token (keep this secure!)
6. Enable the following Privileged Gateway Intents:
   - Server Members Intent
   - Message Content Intent

### Step 2: Install ModuBot
1. Clone or download the ModuBot files
2. Open terminal in the ModuBot directory
3. Run: `npm install`
4. Copy `.env.example` to `.env`
5. Fill in your bot token and other configuration values

### Step 3: Deploy Commands
1. Add your bot's Client ID to `.env`
2. Add your server's Guild ID to `.env` (for faster development)
3. Run: `npm run deploy`

### Step 4: Invite Bot to Server
1. In Developer Portal, go to OAuth2 > URL Generator
2. Select scopes: `bot` and `applications.commands`
3. Select permissions:
   - Manage Channels
   - Manage Roles
   - Kick Members
   - Ban Members
   - Moderate Members
   - Manage Messages
   - Read Message History
   - Send Messages
   - Use Slash Commands
   - Embed Links
   - Attach Files
   - Add Reactions
4. Use generated URL to invite bot

### Step 5: Start the Bot
```bash
npm start
```

---

## Discord Server Creation & Optimization

### Server Template for Large Community/Support
When creating your Discord server, consider this optimized structure:

#### Server Settings
- **Verification Level**: Medium (verified email required)
- **Explicit Content Filter**: Scan messages from members without roles
- **Default Notifications**: Only @mentions
- **2FA Requirement**: Enabled for moderators

#### Community Features
1. Enable Community Server features
2. Set up Welcome Screen with:
   - Server description
   - Rules channel
   - General chat
   - Support information
3. Configure Discovery (if desired)

---

## Permission System & Role Hierarchy

### Recommended Role Structure (Top to Bottom)

#### Administrative Roles
1. **Server Owner** (automatic)
2. **Administrator** 
   - All permissions
   - Distinguished color (red/gold)
3. **Head Moderator**
   - Most moderation permissions
   - Manage channels/roles (limited)

#### Moderation Roles
4. **Senior Moderator**
   - Kick, ban, timeout members
   - Manage messages
   - View audit log
5. **Moderator**
   - Kick members, timeout
   - Manage messages
6. **Trial Moderator**
   - Timeout members
   - Manage messages (limited)

#### Support Roles
7. **Support Lead**
   - Access to all support channels
   - Manage support tickets
8. **Support Staff**
   - Access to support channels
   - Handle tickets

#### Community Roles
9. **VIP/Donor**
   - Special perks and channels
10. **Active Member**
    - Earned through engagement
11. **Verified Member**
    - Basic verification completed
12. **New Member**
    - Default role for new joins

#### Bot Roles
- **ModuBot** (high in hierarchy)
- **Other Bots** (appropriate positions)

### Permission Best Practices
- Use role hierarchy effectively
- Grant minimum necessary permissions
- Regularly audit role permissions
- Use channel-specific overrides when needed

---

## Channel Structure & Organization

### Recommended Channel Layout

#### 📋 INFORMATION
- `#rules` - Server rules and guidelines
- `#announcements` - Important server updates
- `#welcome` - Welcome new members
- `#faq` - Frequently asked questions

#### 💬 GENERAL
- `#general-chat` - Main discussion
- `#introductions` - Member introductions
- `#off-topic` - Casual conversations
- `#memes` - Meme sharing

#### 🎫 SUPPORT
- `#support-info` - How to get help
- `#ticket-logs` - Closed ticket transcripts
- Support ticket channels (created by bot)

#### 🔧 COMMUNITY
- `#suggestions` - Server improvement ideas
- `#feedback` - General feedback
- `#events` - Community events
- `#polls` - Community polls

#### 🎮 ACTIVITIES (if gaming server)
- `#looking-for-group`
- `#game-discussion`
- Game-specific channels

#### 🔒 STAFF
- `#staff-chat` - Staff discussions
- `#mod-logs` - Moderation actions
- `#staff-announcements`
- `#ticket-management`

#### 🔊 VOICE CHANNELS
- General voice channels
- Private voice channels
- AFK channel

### Channel Configuration Tips
- Set appropriate slowmode (3-5 seconds for busy channels)
- Use channel topics for guidance
- Pin important messages
- Configure auto-archive for threads

---

## ModuBot Configuration

### Initial Setup Commands
Run these commands after inviting ModuBot:

1. **Basic Setup**
   ```
   /setup
   ```
   This initializes the database and basic settings.

2. **Configure Moderation Log**
   ```
   /config modlog #mod-logs
   ```

3. **Set Support Category**
   ```
   /config support-category [Support Category ID]
   ```

4. **Configure Welcome System**
   ```
   /config welcome-channel #welcome
   /config welcome-message "Welcome {user} to {server}!"
   ```

### Key Configuration Options

#### Moderation Settings
- **Max Warnings**: Default 3 (configurable)
- **Auto-moderation**: Can be enabled for spam/links
- **Mod Log Channel**: Tracks all moderation actions

#### Support System
- **Ticket Categories**: Bug, General, Feature, Report, Technical, Billing
- **Ticket Limit**: 3 per user (configurable)
- **Auto-close**: Inactive tickets after 7 days

#### XP/Level System
- **XP per Message**: 5 (configurable)
- **XP Cooldown**: 60 seconds
- **Level Announcements**: Enabled by default

### Database Management
ModuBot uses SQLite for data storage:
- User levels and XP
- Warnings and moderation history
- Support tickets
- Server settings
- Custom tags/responses

---

## Complementary Bots & Integrations

### Essential Bots for Large Servers

#### 1. **Carl-bot** (Automoderation & Utilities)
- **Purpose**: Advanced automod, reaction roles, logging
- **Key Features**:
  - Automod for spam, links, mentions
  - Reaction role menus
  - Advanced logging
  - Custom commands
- **Setup Priority**: High
- **Permissions**: Moderate Members, Manage Messages, Manage Roles

#### 2. **Dyno** (Backup Moderation)
- **Purpose**: Backup moderation and music
- **Key Features**:
  - Web dashboard
  - Music commands
  - Additional automod
  - Timed mutes/bans
- **Setup Priority**: Medium

#### 3. **MEE6** (Leveling & Engagement)
- **Purpose**: Alternative/additional leveling system
- **Key Features**:
  - Web dashboard for levels
  - Role rewards
  - Music commands
  - Welcome messages
- **Setup Priority**: Medium (if not using ModuBot's XP system)

#### 4. **Ticket Tool** (Advanced Ticketing)
- **Purpose**: More advanced ticket system if needed
- **Key Features**:
  - Multi-guild support
  - Ticket transcripts
  - Advanced permissions
  - Web panel
- **Setup Priority**: Low (ModuBot has built-in tickets)

#### 5. **Statbot** (Server Statistics)
- **Purpose**: Server analytics and statistics
- **Key Features**:
  - Member count tracking
  - Activity statistics
  - Growth analytics
- **Setup Priority**: Low

### Integration Best Practices
- Avoid overlapping functionality
- Set clear bot hierarchy
- Use different prefixes for each bot
- Regular bot maintenance and updates
- Monitor bot performance and uptime

---

## Server Customization & Branding

### Custom Emojis Strategy
1. **Moderation Emojis**
   - ✅ Approved
   - ❌ Denied
   - ⚠️ Warning
   - 🔒 Locked
   - 🔓 Unlocked

2. **Support Emojis**
   - 🎫 Ticket
   - 🐛 Bug
   - 💡 Feature
   - ❓ Question
   - ⚠️ Report

3. **Reaction Emojis**
   - 👍 Like
   - 👎 Dislike
   - ❤️ Love
   - 😂 Funny
   - 😮 Surprised

4. **Status Emojis**
   - 🟢 Online
   - 🟡 Away
   - 🔴 Busy
   - ⚫ Offline

### Server Icon & Banner
- **Icon**: Clear, recognizable logo (512x512 recommended)
- **Banner**: Professional design matching theme (960x540)
- **Splash**: For partnered servers (1920x1080)

### Color Scheme
Choose a consistent color palette:
- **Primary**: Main brand color
- **Secondary**: Accent color
- **Success**: Green (#00ff00)
- **Warning**: Yellow (#ffff00)
- **Error**: Red (#ff0000)
- **Info**: Blue (#0099ff)

---

## Moderation Best Practices

### Moderation Philosophy
1. **Consistency**: Apply rules equally to all members
2. **Transparency**: Clear rules and consequences
3. **Escalation**: Progressive punishment system
4. **Documentation**: Log all moderation actions
5. **Appeal Process**: Allow appeals for major actions

### Warning System
ModuBot implements a 3-strike system:
1. **First Warning**: Verbal warning + log
2. **Second Warning**: Timeout + log
3. **Third Warning**: Kick/ban consideration

### Common Moderation Scenarios

#### Spam/Flooding
- **First Offense**: Delete messages + warning
- **Repeat Offense**: Timeout (1-24 hours)
- **Persistent**: Kick or ban

#### Inappropriate Content
- **NSFW in SFW channels**: Immediate delete + warning
- **Hate speech**: Immediate timeout/ban
- **Harassment**: Investigate + appropriate action

#### Rule Violations
- **Minor**: Warning + explanation
- **Major**: Timeout + warning
- **Severe**: Kick or ban

### Staff Training
1. **Rule Knowledge**: All staff must know rules thoroughly
2. **Bot Commands**: Training on ModuBot commands
3. **Escalation Procedures**: When to involve higher staff
4. **Documentation**: How to properly log actions

---

## Community Engagement Strategies

### Welcome Process
1. **Automated Welcome**: ModuBot sends welcome message
2. **Role Assignment**: Auto-assign "New Member" role
3. **Verification**: Optional verification process
4. **Introduction**: Encourage #introductions post

### Engagement Features
1. **XP System**: Reward active participation
2. **Events**: Regular community events
3. **Polls**: Use ModuBot's poll system for decisions
4. **Giveaways**: Regular giveaways for engagement
5. **Recognition**: Highlight helpful members

### Content Strategy
1. **Regular Announcements**: Keep community informed
2. **Discussion Topics**: Start conversations
3. **Q&A Sessions**: Regular community Q&As
4. **Feedback Collection**: Regular feedback requests

### Growth Strategies
1. **Invite Rewards**: Reward members for invites
2. **Partner Servers**: Cross-promotion
3. **Social Media**: Promote on other platforms
4. **SEO**: Optimize server for Discord discovery

---

## Troubleshooting & Maintenance

### Common Issues & Solutions

#### Bot Not Responding
1. Check bot status and uptime
2. Verify permissions
3. Check rate limits
4. Restart bot if necessary

#### Commands Not Working
1. Verify slash commands are deployed
2. Check bot permissions
3. Ensure user has required permissions
4. Check for typos in command names

#### Database Issues
1. Check database file permissions
2. Verify database integrity
3. Backup database regularly
4. Monitor database size

#### Permission Problems
1. Review role hierarchy
2. Check channel overrides
3. Verify bot role position
4. Test permissions systematically

### Regular Maintenance Tasks

#### Daily
- Monitor server activity
- Check for spam/rule violations
- Review support tickets
- Update announcements if needed

#### Weekly
- Review moderation logs
- Update server statistics
- Check bot performance
- Backup important data

#### Monthly
- Audit permissions and roles
- Review and update rules
- Analyze server growth
- Update bot and dependencies
- Clean up inactive channels

### Performance Optimization
1. **Database Optimization**: Regular cleanup of old data
2. **Bot Monitoring**: Monitor memory and CPU usage
3. **Rate Limit Management**: Avoid hitting Discord limits
4. **Caching**: Implement proper caching strategies

### Backup Strategy
1. **Database Backups**: Daily automated backups
2. **Configuration Backups**: Save bot settings
3. **Server Template**: Create server template backup
4. **Documentation**: Keep setup documentation updated

---

## Advanced Features & Customization

### Custom Commands
ModuBot supports custom tags/responses:
```
/tag create welcome Welcome to our server! Please read #rules
/tag create support Need help? Create a ticket with /ticket
```

### Automation Ideas
1. **Auto-role Assignment**: Based on reactions or verification
2. **Scheduled Announcements**: Regular community updates
3. **Auto-moderation**: Spam and link filtering
4. **Backup Systems**: Multiple bots for redundancy

### Analytics & Monitoring
1. **Server Statistics**: Track growth and activity
2. **Bot Performance**: Monitor uptime and response times
3. **User Engagement**: Track XP and participation
4. **Moderation Metrics**: Review action frequency

---

## Conclusion

This comprehensive guide provides everything needed to set up and maintain a professional Discord server with ModuBot. Remember that community building is an ongoing process that requires consistent effort, clear communication, and adaptability to your community's needs.

For additional support or questions about ModuBot, refer to the bot's help commands or contact the development team.

**Key Success Factors:**
- Consistent moderation
- Clear communication
- Regular engagement
- Continuous improvement
- Community feedback integration

Good luck building your Discord community! 🚀

---

## Quick Start Checklist

### Pre-Launch (Before Inviting Members)
- [ ] Create Discord application and bot
- [ ] Install and configure ModuBot
- [ ] Set up basic server structure (channels, roles)
- [ ] Configure bot permissions and settings
- [ ] Test all major bot functions
- [ ] Create server rules and guidelines
- [ ] Set up moderation logging
- [ ] Configure welcome system
- [ ] Add essential complementary bots
- [ ] Create server icon and branding

### Launch Day
- [ ] Announce server opening
- [ ] Monitor for issues
- [ ] Welcome first members personally
- [ ] Test support ticket system
- [ ] Ensure moderation tools work
- [ ] Gather initial feedback

### Post-Launch (First Week)
- [ ] Monitor server activity and engagement
- [ ] Adjust bot settings based on usage
- [ ] Address any technical issues
- [ ] Collect member feedback
- [ ] Fine-tune moderation policies
- [ ] Plan first community events

### Ongoing Maintenance
- [ ] Weekly bot performance review
- [ ] Monthly permission audit
- [ ] Regular rule updates
- [ ] Community feedback sessions
- [ ] Bot updates and maintenance
- [ ] Backup important data

---

## Emergency Procedures

### Bot Downtime
1. Announce downtime in server
2. Activate backup moderation bots
3. Increase manual moderation
4. Investigate and fix issues
5. Restore normal operations
6. Post-incident review

### Server Raid/Attack
1. Enable slowmode on all channels
2. Temporarily restrict new member permissions
3. Use ModuBot's lockdown features
4. Ban/kick malicious users
5. Review and strengthen security
6. Communicate with community

### Data Loss
1. Stop bot operations immediately
2. Restore from latest backup
3. Assess data loss extent
4. Communicate with affected users
5. Implement additional backup measures
6. Document incident for future prevention

Remember: A well-prepared server is a successful server! 🛡️
