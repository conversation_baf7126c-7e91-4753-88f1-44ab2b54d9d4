const { Events } = require('discord.js');
const { logger } = require('../utils/logger');
const { db } = require('../utils/database');

module.exports = {
    name: Events.MessageReactionAdd,
    async execute(reaction, user) {
        // Ignore bot reactions
        if (user.bot) return;

        try {
            // Handle partial reactions (fetch if needed)
            if (reaction.partial) {
                try {
                    await reaction.fetch();
                } catch (error) {
                    logger.error('Error fetching reaction:', error);
                    return;
                }
            }

            // Handle verification reactions
            await handleVerificationReaction(reaction, user);
            
            // Handle other reaction roles here if needed
            // await handleOtherReactionRoles(reaction, user);
            
        } catch (error) {
            logger.error('Error in messageReactionAdd event:', error);
        }
    },
};

/**
 * Handle verification reaction roles
 */
async function handleVerificationReaction(reaction, user) {
    try {
        const { message, emoji } = reaction;
        const { guild } = message;
        
        // Only handle ✅ reactions
        if (emoji.name !== '✅') return;
        
        // Check if this is a verification message
        const isVerificationMessage = await isVerificationChannel(message);
        if (!isVerificationMessage) return;
        
        const member = await guild.members.fetch(user.id).catch(() => null);
        if (!member) return;
        
        // Find the roles we need
        const newMemberRole = guild.roles.cache.find(role => 
            role.name.includes('New Member') || role.name.includes('👶')
        );
        const verifiedRole = guild.roles.cache.find(role => 
            role.name.includes('Verified Member') || role.name.includes('✅')
        );
        
        if (!verifiedRole) {
            logger.warn(`No verified member role found in guild ${guild.id}`);
            return;
        }
        
        // Check if user already has verified role
        if (member.roles.cache.has(verifiedRole.id)) {
            logger.info(`User ${user.tag} already has verified role in ${guild.name}`);
            return;
        }
        
        // Assign verified role and remove new member role
        const rolesToAdd = [verifiedRole.id];
        const rolesToRemove = [];
        
        if (newMemberRole && member.roles.cache.has(newMemberRole.id)) {
            rolesToRemove.push(newMemberRole.id);
        }
        
        // Add verified role
        try {
            await member.roles.add(verifiedRole, 'User verified through reaction role');
            logger.info(`Added verified role to ${user.tag} in ${guild.name}`);
            
            // Remove new member role if they have it
            if (rolesToRemove.length > 0) {
                await member.roles.remove(rolesToRemove, 'User verified - removing new member role');
                logger.info(`Removed new member role from ${user.tag} in ${guild.name}`);
            }
            
            // Log verification to database
            await logVerification(user.id, guild.id, verifiedRole.id);
            
            // Send welcome DM (optional)
            await sendVerificationWelcome(member, guild);
            
        } catch (error) {
            logger.error(`Error assigning roles to ${user.tag} in ${guild.name}:`, error);
            
            // Try to send error message to user
            try {
                await user.send(`❌ There was an error verifying you in **${guild.name}**. Please contact a staff member for assistance.`);
            } catch (dmError) {
                logger.warn(`Could not send DM to ${user.tag}:`, dmError);
            }
        }
        
    } catch (error) {
        logger.error('Error in handleVerificationReaction:', error);
    }
}

/**
 * Check if a message is in a verification channel
 */
async function isVerificationChannel(message) {
    try {
        const { channel } = message;
        
        // Check if channel name contains verification keywords
        const verificationKeywords = ['verification', 'verify', 'welcome'];
        const channelName = channel.name.toLowerCase();
        
        if (verificationKeywords.some(keyword => channelName.includes(keyword))) {
            // Additional check: see if the message contains verification content
            const messageContent = message.content.toLowerCase();
            if (messageContent.includes('verify') || messageContent.includes('react') || messageContent.includes('✅')) {
                return true;
            }
        }
        
        return false;
    } catch (error) {
        logger.error('Error checking verification channel:', error);
        return false;
    }
}

/**
 * Log verification to database
 */
async function logVerification(userId, guildId, roleId) {
    try {
        await db.run(
            `INSERT INTO verification_logs (user_id, guild_id, role_id, verified_at) 
             VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
            [userId, guildId, roleId]
        );
    } catch (error) {
        // Don't fail verification if logging fails
        logger.warn('Error logging verification:', error);
    }
}

/**
 * Send welcome DM to newly verified member
 */
async function sendVerificationWelcome(member, guild) {
    try {
        const welcomeMessage = `🎉 **Welcome to ${guild.name}!**

You've been successfully verified and now have access to all public channels.

🚀 **Next Steps:**
• Introduce yourself in the introductions channel
• Check out the server guide for navigation help
• Join the conversation in general chat
• Use \`/help\` to see available bot commands

If you need any help, feel free to ask in the general chat or create a support ticket!

Welcome to the community! 🎊`;

        await member.send(welcomeMessage);
        logger.info(`Sent welcome DM to ${member.user.tag} in ${guild.name}`);
    } catch (error) {
        // Don't fail verification if DM fails
        logger.warn(`Could not send welcome DM to ${member.user.tag}:`, error);
    }
}
