const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder, ChannelType } = require('discord.js');
const { db } = require('../../utils/database');
const { logger } = require('../../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Initial setup for ModuBot')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setDMPermission(false),
    
    async execute(interaction) {
        await interaction.deferReply();
        
        try {
            const guildId = interaction.guild.id;
            
            // Check if guild settings already exist
            const existingSettings = await db.get(
                'SELECT * FROM guild_settings WHERE guild_id = ?',
                [guildId]
            );
            
            if (existingSettings) {
                const embed = new EmbedBuilder()
                    .setColor(0xffff00)
                    .setTitle('⚠️ Setup Already Complete')
                    .setDescription('ModuBot has already been set up for this server.')
                    .addFields(
                        { name: 'Current Settings', value: 'Use `/config` to modify settings', inline: false }
                    )
                    .setTimestamp();
                
                return interaction.editReply({ embeds: [embed] });
            }
            
            // Create default guild settings
            await db.run(
                `INSERT INTO guild_settings (guild_id, prefix, max_warnings, xp_enabled) 
                 VALUES (?, ?, ?, ?)`,
                [guildId, '!', 3, 1]
            );
            
            // Try to create support category if it doesn't exist
            let supportCategory = interaction.guild.channels.cache.find(
                channel => channel.type === ChannelType.GuildCategory && 
                channel.name.toLowerCase().includes('support')
            );
            
            if (!supportCategory) {
                try {
                    supportCategory = await interaction.guild.channels.create({
                        name: '🎫 SUPPORT',
                        type: ChannelType.GuildCategory,
                        permissionOverwrites: [
                            {
                                id: interaction.guild.roles.everyone.id,
                                deny: [PermissionFlagsBits.SendMessages]
                            }
                        ]
                    });
                    
                    // Create support info channel
                    await interaction.guild.channels.create({
                        name: 'support-info',
                        type: ChannelType.GuildText,
                        parent: supportCategory.id,
                        topic: 'Information about getting support - Use /ticket to create a support ticket',
                        permissionOverwrites: [
                            {
                                id: interaction.guild.roles.everyone.id,
                                deny: [PermissionFlagsBits.SendMessages],
                                allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.ReadMessageHistory]
                            }
                        ]
                    });
                    
                } catch (error) {
                    logger.warn('Could not create support category:', error);
                }
            }
            
            // Try to create mod log channel if it doesn't exist
            let modLogChannel = interaction.guild.channels.cache.find(
                channel => channel.name.includes('mod-log') || channel.name.includes('modlog')
            );
            
            if (!modLogChannel) {
                try {
                    modLogChannel = await interaction.guild.channels.create({
                        name: 'mod-logs',
                        type: ChannelType.GuildText,
                        topic: 'Moderation action logs',
                        permissionOverwrites: [
                            {
                                id: interaction.guild.roles.everyone.id,
                                deny: [PermissionFlagsBits.ViewChannel]
                            }
                        ]
                    });
                } catch (error) {
                    logger.warn('Could not create mod log channel:', error);
                }
            }
            
            // Update settings with created channels
            if (supportCategory) {
                await db.run(
                    'UPDATE guild_settings SET support_category = ? WHERE guild_id = ?',
                    [supportCategory.id, guildId]
                );
            }
            
            if (modLogChannel) {
                await db.run(
                    'UPDATE guild_settings SET mod_log_channel = ? WHERE guild_id = ?',
                    [modLogChannel.id, guildId]
                );
            }
            
            // Create setup complete embed
            const setupEmbed = new EmbedBuilder()
                .setColor(0x00ff00)
                .setTitle('✅ Setup Complete!')
                .setDescription('ModuBot has been successfully set up for your server.')
                .addFields(
                    { name: '🎫 Support System', value: supportCategory ? `Category: ${supportCategory}` : 'Manual setup required', inline: false },
                    { name: '📋 Mod Logs', value: modLogChannel ? `Channel: ${modLogChannel}` : 'Manual setup required', inline: false },
                    { name: '⚙️ Default Settings', value: 'Prefix: `!`\nMax Warnings: 3\nXP System: Enabled', inline: false },
                    { name: '📚 Next Steps', value: '• Use `/config` to customize settings\n• Set up additional channels as needed\n• Configure permissions for staff roles\n• Test bot functionality', inline: false }
                )
                .setFooter({ text: 'Use /help to see all available commands' })
                .setTimestamp();
            
            await interaction.editReply({ embeds: [setupEmbed] });
            
            logger.info(`ModuBot setup completed for ${interaction.guild.name} by ${interaction.user.tag}`);
            
        } catch (error) {
            logger.error('Error during setup:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor(0xff0000)
                .setTitle('❌ Setup Error')
                .setDescription('An error occurred during setup. Please try again or contact support.')
                .addFields(
                    { name: 'Error Details', value: 'Check bot permissions and try again', inline: false }
                )
                .setTimestamp();
            
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};
