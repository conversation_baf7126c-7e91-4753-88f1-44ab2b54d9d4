<!-- Hero Section -->
<section class="hero-section py-5">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold text-white mb-4">
                        Professional Discord Server Management
                    </h1>
                    <p class="lead text-white-50 mb-4">
                        ModuBot provides comprehensive moderation, automation, and community management tools 
                        for Discord servers of all sizes. Manage your community with ease through our 
                        intuitive dashboard.
                    </p>
                    
                    <div class="hero-buttons mb-4">
                        <% if (user) { %>
                            <a href="/dashboard" class="btn btn-discord btn-lg me-3">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Go to Dashboard
                            </a>
                        <% } else { %>
                            <a href="/auth/discord" class="btn btn-discord btn-lg me-3">
                                <i class="fab fa-discord me-2"></i>
                                Login with Discord
                            </a>
                        <% } %>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>
                            Learn More
                        </a>
                    </div>
                    
                    <div class="hero-stats">
                        <div class="row text-center text-white">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="fw-bold">1000+</h3>
                                    <p class="small">Servers</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="fw-bold">50K+</h3>
                                    <p class="small">Users</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="fw-bold">99.9%</h3>
                                    <p class="small">Uptime</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <div class="dashboard-preview">
                        <div class="card shadow-lg">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon me-3">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0">ModuBot Dashboard</h5>
                                        <small class="text-muted">Server Management</small>
                                    </div>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="stats-card card">
                                            <div class="card-body text-center">
                                                <div class="stats-number">1,234</div>
                                                <div class="stats-label">Members</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stats-card card">
                                            <div class="card-body text-center">
                                                <div class="stats-number">89</div>
                                                <div class="stats-label">Online</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small">Server Activity</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-gradient" style="width: 75%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold text-white mb-3">Powerful Features</h2>
                <p class="lead text-white-50">
                    Everything you need to manage and grow your Discord community
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mx-auto mb-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5 class="card-title">Advanced Moderation</h5>
                        <p class="card-text text-muted">
                            Comprehensive moderation tools including auto-mod, warnings, 
                            kicks, bans, and detailed logging.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mx-auto mb-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="card-title">Member Management</h5>
                        <p class="card-text text-muted">
                            Role management, verification systems, welcome messages, 
                            and member progression tracking.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mx-auto mb-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5 class="card-title">Analytics & Insights</h5>
                        <p class="card-text text-muted">
                            Detailed server analytics, member growth tracking, 
                            and engagement metrics.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mx-auto mb-3">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h5 class="card-title">Easy Configuration</h5>
                        <p class="card-text text-muted">
                            Intuitive web dashboard for easy bot configuration 
                            and server management.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mx-auto mb-3">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h5 class="card-title">Support System</h5>
                        <p class="card-text text-muted">
                            Built-in ticket system for member support and 
                            staff communication.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mx-auto mb-3">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h5 class="card-title">Professional Setup</h5>
                        <p class="card-text text-muted">
                            One-click professional server setup with roles, 
                            channels, and permissions.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <div class="card">
                    <div class="card-body p-5">
                        <h3 class="fw-bold mb-3">Ready to Get Started?</h3>
                        <p class="text-muted mb-4">
                            Join thousands of Discord communities using ModuBot for professional server management.
                        </p>
                        
                        <% if (user) { %>
                            <a href="/dashboard" class="btn btn-discord btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Access Dashboard
                            </a>
                        <% } else { %>
                            <a href="/auth/discord" class="btn btn-discord btn-lg">
                                <i class="fab fa-discord me-2"></i>
                                Login with Discord
                            </a>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .hero-section {
        min-height: 100vh;
        display: flex;
        align-items: center;
    }
    
    .dashboard-preview {
        transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
        transition: transform 0.3s ease;
    }
    
    .dashboard-preview:hover {
        transform: perspective(1000px) rotateY(-10deg) rotateX(5deg);
    }
    
    .stat-item h3 {
        font-size: 2rem;
        margin-bottom: 0.25rem;
    }
    
    .progress-bar {
        background: linear-gradient(90deg, var(--discord-blurple), #764ba2);
    }
</style>
