<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="p-3">
                <h6 class="text-muted text-uppercase fw-bold mb-3">Navigation</h6>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                    <a class="nav-link" href="#servers">
                        <i class="fas fa-server me-2"></i>
                        My Servers
                    </a>
                    <a class="nav-link" href="#analytics">
                        <i class="fas fa-chart-bar me-2"></i>
                        Analytics
                    </a>
                    <a class="nav-link" href="#settings">
                        <i class="fas fa-cog me-2"></i>
                        Settings
                    </a>
                </nav>
                
                <hr class="my-4">
                
                <h6 class="text-muted text-uppercase fw-bold mb-3">Quick Actions</h6>
                <nav class="nav flex-column">
                    <a class="nav-link" href="#invite">
                        <i class="fas fa-plus me-2"></i>
                        Invite Bot
                    </a>
                    <a class="nav-link" href="#support">
                        <i class="fas fa-life-ring me-2"></i>
                        Support
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <!-- Welcome Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 fw-bold">Welcome back, <%= user.username %>!</h1>
                    <p class="text-muted">Manage your Discord servers with ModuBot</p>
                </div>
                <div>
                    <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" 
                         alt="Avatar" class="rounded-circle" width="48" height="48">
                </div>
            </div>
            
            <!-- Stats Overview -->
            <div class="row g-4 mb-5">
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number"><%= guilds.length %></div>
                            <div class="stats-label">Managed Servers</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number">24/7</div>
                            <div class="stats-label">Bot Uptime</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number">100+</div>
                            <div class="stats-label">Commands</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number">Pro</div>
                            <div class="stats-label">Features</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Server List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-server me-2"></i>
                                Your Servers
                            </h5>
                            <a href="#invite" class="btn btn-discord btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Invite Bot
                            </a>
                        </div>
                        <div class="card-body">
                            <% if (guilds.length === 0) { %>
                                <div class="text-center py-5">
                                    <div class="feature-icon mx-auto mb-3">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <h5>No Managed Servers</h5>
                                    <p class="text-muted mb-4">
                                        You don't have any servers where you can manage ModuBot yet.
                                    </p>
                                    <a href="#invite" class="btn btn-discord">
                                        <i class="fas fa-plus me-2"></i>
                                        Invite ModuBot to Your Server
                                    </a>
                                </div>
                            <% } else { %>
                                <div class="row g-4">
                                    <% guilds.forEach(guild => { %>
                                        <div class="col-md-6 col-lg-4">
                                            <div class="guild-card card h-100" onclick="window.location.href='/server/<%= guild.id %>'">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-center mb-3">
                                                        <% if (guild.icon) { %>
                                                            <img src="https://cdn.discordapp.com/icons/<%= guild.id %>/<%= guild.icon %>.png" 
                                                                 alt="<%= guild.name %>" class="guild-icon me-3">
                                                        <% } else { %>
                                                            <div class="guild-icon me-3 bg-secondary d-flex align-items-center justify-content-center text-white fw-bold">
                                                                <%= guild.name.charAt(0).toUpperCase() %>
                                                            </div>
                                                        <% } %>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-1"><%= guild.name %></h6>
                                                            <span class="status-badge status-online">
                                                                <i class="fas fa-circle me-1"></i>
                                                                Online
                                                            </span>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="row g-2 text-center">
                                                        <div class="col-6">
                                                            <div class="small text-muted">Members</div>
                                                            <div class="fw-bold">-</div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="small text-muted">Online</div>
                                                            <div class="fw-bold">-</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mt-3">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <span class="small text-muted">Setup Status</span>
                                                            <span class="badge bg-success">Complete</span>
                                                        </div>
                                                        <div class="progress" style="height: 6px;">
                                                            <div class="progress-bar bg-success" style="width: 100%"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-footer bg-transparent">
                                                    <div class="d-flex justify-content-between">
                                                        <small class="text-muted">
                                                            <i class="fas fa-crown me-1"></i>
                                                            <% if ((guild.permissions & 0x8) === 0x8) { %>
                                                                Administrator
                                                            <% } else { %>
                                                                Manager
                                                            <% } %>
                                                        </small>
                                                        <small class="text-muted">
                                                            <i class="fas fa-arrow-right"></i>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <% }); %>
                                </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <a href="#invite" class="btn btn-outline-primary w-100 p-3">
                                        <i class="fas fa-plus fa-2x mb-2 d-block"></i>
                                        <strong>Invite Bot</strong>
                                        <small class="d-block text-muted">Add ModuBot to a new server</small>
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="#setup" class="btn btn-outline-success w-100 p-3">
                                        <i class="fas fa-magic fa-2x mb-2 d-block"></i>
                                        <strong>Professional Setup</strong>
                                        <small class="d-block text-muted">Configure server automatically</small>
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="#support" class="btn btn-outline-info w-100 p-3">
                                        <i class="fas fa-life-ring fa-2x mb-2 d-block"></i>
                                        <strong>Get Support</strong>
                                        <small class="d-block text-muted">Need help? Contact us</small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invite Bot Modal -->
<div class="modal fade" id="inviteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    Invite ModuBot
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Click the button below to invite ModuBot to your Discord server:</p>
                <div class="text-center">
                    <a href="https://discord.com/api/oauth2/authorize?client_id=YOUR_BOT_CLIENT_ID&permissions=8&scope=bot%20applications.commands" 
                       target="_blank" class="btn btn-discord btn-lg">
                        <i class="fab fa-discord me-2"></i>
                        Invite to Discord
                    </a>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Required Permissions:</strong> Administrator (for full functionality)
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Handle invite bot links
    document.querySelectorAll('a[href="#invite"]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('inviteModal'));
            modal.show();
        });
    });
</script>
