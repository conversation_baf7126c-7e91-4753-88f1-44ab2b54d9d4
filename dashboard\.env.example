# Discord OAuth2 Configuration
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_CLIENT_SECRET=your_discord_client_secret_here
DISCORD_CALLBACK_URL=http://localhost:3000/auth/discord/callback

# Session Configuration
SESSION_SECRET=your_super_secret_session_key_change_this_in_production

# Database Configuration
DATABASE_PATH=../data/modubot.db

# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Bot Configuration (for API integration)
BOT_TOKEN=your_bot_token_here
BOT_CLIENT_ID=your_bot_client_id_here

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Analytics Configuration (optional)
GOOGLE_ANALYTICS_ID=your_ga_id_here

# Webhook Configuration (optional)
WEBHOOK_SECRET=your_webhook_secret_here
