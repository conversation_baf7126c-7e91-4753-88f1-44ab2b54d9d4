const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Get help with ModuBot commands')
        .addStringOption(option =>
            option.setName('command')
                .setDescription('Get detailed help for a specific command')
                .setRequired(false)),
    
    async execute(interaction) {
        const commandName = interaction.options.getString('command');
        
        if (commandName) {
            // Show specific command help
            const command = interaction.client.commands.get(commandName);
            
            if (!command) {
                return interaction.reply({
                    content: `❌ Command \`${commandName}\` not found.`,
                    ephemeral: true
                });
            }
            
            const embed = new EmbedBuilder()
                .setColor(0x0099ff)
                .setTitle(`📖 Help: /${command.data.name}`)
                .setDescription(command.data.description)
                .addFields(
                    {
                        name: 'Usage',
                        value: `\`/${command.data.name}\``,
                        inline: true
                    },
                    {
                        name: 'Cooldown',
                        value: `${command.cooldown || 3} seconds`,
                        inline: true
                    }
                );
            
            // Add options if they exist
            if (command.data.options && command.data.options.length > 0) {
                const options = command.data.options.map(option => {
                    const required = option.required ? '**Required**' : 'Optional';
                    return `**${option.name}** (${option.type}) - ${required}\n${option.description}`;
                }).join('\n\n');
                
                embed.addFields({
                    name: 'Options',
                    value: options,
                    inline: false
                });
            }
            
            return interaction.reply({ embeds: [embed], ephemeral: true });
        }
        
        // Show general help with category selection
        const embed = new EmbedBuilder()
            .setColor(0x0099ff)
            .setTitle('🤖 ModuBot Help')
            .setDescription('Welcome to ModuBot! Select a category below to see available commands.')
            .addFields(
                {
                    name: '🛡️ Moderation',
                    value: 'Commands for server moderation and management',
                    inline: true
                },
                {
                    name: '🎫 Support',
                    value: 'Ticket system and support tools',
                    inline: true
                },
                {
                    name: '🔧 Utility',
                    value: 'Useful utility and information commands',
                    inline: true
                },
                {
                    name: '🎉 Fun',
                    value: 'Entertainment and engagement commands',
                    inline: true
                },
                {
                    name: '🏆 Levels',
                    value: 'XP and leveling system commands',
                    inline: true
                },
                {
                    name: '⚙️ Settings',
                    value: 'Server configuration and setup',
                    inline: true
                }
            )
            .setFooter({
                text: 'Use /help <command> for detailed information about a specific command'
            })
            .setTimestamp();
        
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('help_category')
            .setPlaceholder('Select a command category')
            .addOptions([
                {
                    label: 'Moderation',
                    description: 'Server moderation commands',
                    value: 'moderation',
                    emoji: '🛡️'
                },
                {
                    label: 'Support',
                    description: 'Ticket and support system',
                    value: 'support',
                    emoji: '🎫'
                },
                {
                    label: 'Utility',
                    description: 'Utility and information commands',
                    value: 'utility',
                    emoji: '🔧'
                },
                {
                    label: 'Fun',
                    description: 'Fun and entertainment commands',
                    value: 'fun',
                    emoji: '🎉'
                },
                {
                    label: 'Levels',
                    description: 'XP and leveling commands',
                    value: 'levels',
                    emoji: '🏆'
                },
                {
                    label: 'Settings',
                    description: 'Server configuration commands',
                    value: 'settings',
                    emoji: '⚙️'
                }
            ]);
        
        const row = new ActionRowBuilder().addComponents(selectMenu);
        
        await interaction.reply({
            embeds: [embed],
            components: [row],
            ephemeral: true
        });
    },
};

// Command categories for the help system
const commandCategories = {
    moderation: {
        title: '🛡️ Moderation Commands',
        commands: [
            { name: 'kick', description: 'Kick a member from the server' },
            { name: 'ban', description: 'Ban a member from the server' },
            { name: 'unban', description: 'Unban a user from the server' },
            { name: 'warn', description: 'Warn a member' },
            { name: 'warnings', description: 'View warnings for a user' },
            { name: 'timeout', description: 'Timeout a member' },
            { name: 'purge', description: 'Bulk delete messages' },
            { name: 'slowmode', description: 'Set channel slowmode' },
            { name: 'lockdown', description: 'Lock/unlock a channel' }
        ]
    },
    support: {
        title: '🎫 Support Commands',
        commands: [
            { name: 'ticket', description: 'Create a support ticket' },
            { name: 'close-ticket', description: 'Close a support ticket' },
            { name: 'add-user', description: 'Add user to ticket' },
            { name: 'remove-user', description: 'Remove user from ticket' },
            { name: 'ticket-setup', description: 'Setup ticket system' }
        ]
    },
    utility: {
        title: '🔧 Utility Commands',
        commands: [
            { name: 'userinfo', description: 'Get information about a user' },
            { name: 'serverinfo', description: 'Get information about the server' },
            { name: 'avatar', description: 'Get user\'s avatar' },
            { name: 'remind', description: 'Set a reminder' },
            { name: 'poll', description: 'Create a poll' },
            { name: 'weather', description: 'Get weather information' },
            { name: 'translate', description: 'Translate text' }
        ]
    },
    fun: {
        title: '🎉 Fun Commands',
        commands: [
            { name: 'roll', description: 'Roll dice' },
            { name: '8ball', description: 'Ask the magic 8-ball' },
            { name: 'flip', description: 'Flip a coin' },
            { name: 'quote', description: 'Get an inspirational quote' },
            { name: 'joke', description: 'Get a random joke' }
        ]
    },
    levels: {
        title: '🏆 Level Commands',
        commands: [
            { name: 'level', description: 'Check your or someone\'s level' },
            { name: 'leaderboard', description: 'View server leaderboard' },
            { name: 'rank', description: 'View your rank card' }
        ]
    },
    settings: {
        title: '⚙️ Settings Commands',
        commands: [
            { name: 'setup', description: 'Initial bot setup' },
            { name: 'config', description: 'Configure bot settings' },
            { name: 'prefix', description: 'Change bot prefix' },
            { name: 'modlog', description: 'Set moderation log channel' }
        ]
    }
};

module.exports.commandCategories = commandCategories;
