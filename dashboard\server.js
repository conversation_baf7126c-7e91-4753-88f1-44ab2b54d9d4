const express = require('express');
const session = require('express-session');
const passport = require('passport');
const DiscordStrategy = require('passport-discord').Strategy;
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https://cdn.discordapp.com", "https://images.unsplash.com"]
        }
    }
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// CORS configuration
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key-change-this',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Passport configuration
app.use(passport.initialize());
app.use(passport.session());

// Discord OAuth2 Strategy
passport.use(new DiscordStrategy({
    clientID: process.env.DISCORD_CLIENT_ID,
    clientSecret: process.env.DISCORD_CLIENT_SECRET,
    callbackURL: process.env.DISCORD_CALLBACK_URL || 'http://localhost:3000/auth/discord/callback',
    scope: ['identify', 'guilds']
}, async (accessToken, refreshToken, profile, done) => {
    try {
        // Store user info in session
        const user = {
            id: profile.id,
            username: profile.username,
            discriminator: profile.discriminator,
            avatar: profile.avatar,
            guilds: profile.guilds || []
        };
        return done(null, user);
    } catch (error) {
        return done(error, null);
    }
}));

passport.serializeUser((user, done) => {
    done(null, user);
});

passport.deserializeUser((user, done) => {
    done(null, user);
});

// Database connection
const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '..', 'data', 'modubot.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('Error connecting to database:', err);
    } else {
        console.log('Connected to ModuBot database');
    }
});

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Authentication middleware
function ensureAuthenticated(req, res, next) {
    if (req.isAuthenticated()) {
        return next();
    }
    res.redirect('/auth/discord');
}

// Routes
app.get('/', (req, res) => {
    res.render('index', { 
        user: req.user,
        title: 'ModuBot Dashboard'
    });
});

// Authentication routes
app.get('/auth/discord', passport.authenticate('discord'));

app.get('/auth/discord/callback',
    passport.authenticate('discord', { failureRedirect: '/' }),
    (req, res) => {
        res.redirect('/dashboard');
    }
);

app.get('/logout', (req, res) => {
    req.logout((err) => {
        if (err) {
            console.error('Logout error:', err);
        }
        res.redirect('/');
    });
});

// Dashboard routes
app.get('/dashboard', ensureAuthenticated, async (req, res) => {
    try {
        // Get user's guilds where ModuBot is present
        const userGuilds = req.user.guilds || [];
        const managedGuilds = userGuilds.filter(guild => 
            (guild.permissions & 0x8) === 0x8 || // Administrator
            (guild.permissions & 0x20) === 0x20   // Manage Server
        );

        res.render('dashboard', {
            user: req.user,
            guilds: managedGuilds,
            title: 'Dashboard - ModuBot'
        });
    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('error', { 
            error: 'Failed to load dashboard',
            title: 'Error - ModuBot'
        });
    }
});

// Server management routes
app.get('/server/:guildId', ensureAuthenticated, async (req, res) => {
    try {
        const guildId = req.params.guildId;
        
        // Verify user has access to this guild
        const userGuild = req.user.guilds?.find(g => g.id === guildId);
        if (!userGuild || !((userGuild.permissions & 0x8) === 0x8 || (userGuild.permissions & 0x20) === 0x20)) {
            return res.status(403).render('error', { 
                error: 'Access denied to this server',
                title: 'Access Denied - ModuBot'
            });
        }

        // Get server data from database
        const serverData = await getServerData(guildId);
        
        res.render('server', {
            user: req.user,
            guild: userGuild,
            serverData: serverData,
            title: `${userGuild.name} - ModuBot`
        });
    } catch (error) {
        console.error('Server page error:', error);
        res.status(500).render('error', { 
            error: 'Failed to load server data',
            title: 'Error - ModuBot'
        });
    }
});

// API routes for server data
app.get('/api/server/:guildId/stats', ensureAuthenticated, async (req, res) => {
    try {
        const guildId = req.params.guildId;
        const stats = await getServerStats(guildId);
        res.json(stats);
    } catch (error) {
        console.error('Stats API error:', error);
        res.status(500).json({ error: 'Failed to fetch stats' });
    }
});

// Helper functions
async function getServerData(guildId) {
    return new Promise((resolve, reject) => {
        db.get('SELECT * FROM guild_settings WHERE guild_id = ?', [guildId], (err, settings) => {
            if (err) {
                reject(err);
            } else {
                resolve(settings || {});
            }
        });
    });
}

async function getServerStats(guildId) {
    return new Promise((resolve, reject) => {
        const stats = {};
        
        // Get member join stats
        db.all(`
            SELECT DATE(timestamp) as date, COUNT(*) as joins 
            FROM member_logs 
            WHERE guild_id = ? AND action = 'join' 
            AND timestamp >= datetime('now', '-30 days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        `, [guildId], (err, joinData) => {
            if (err) {
                reject(err);
            } else {
                stats.memberJoins = joinData;
                
                // Get verification stats
                db.all(`
                    SELECT DATE(verified_at) as date, COUNT(*) as verifications
                    FROM verification_logs
                    WHERE guild_id = ?
                    AND verified_at >= datetime('now', '-30 days')
                    GROUP BY DATE(verified_at)
                    ORDER BY date
                `, [guildId], (err, verificationData) => {
                    if (err) {
                        reject(err);
                    } else {
                        stats.verifications = verificationData;
                        resolve(stats);
                    }
                });
            }
        });
    });
}

// Error handling
app.use((req, res) => {
    res.status(404).render('error', { 
        error: 'Page not found',
        title: '404 - ModuBot'
    });
});

app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).render('error', { 
        error: 'Internal server error',
        title: 'Error - ModuBot'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 ModuBot Dashboard running on port ${PORT}`);
    console.log(`📊 Dashboard URL: http://localhost:${PORT}`);
});

module.exports = app;
