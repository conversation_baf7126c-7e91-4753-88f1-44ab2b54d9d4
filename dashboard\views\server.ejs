<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="p-3">
                <!-- Server Info -->
                <div class="text-center mb-4">
                    <% if (guild.icon) { %>
                        <img src="https://cdn.discordapp.com/icons/<%= guild.id %>/<%= guild.icon %>.png" 
                             alt="<%= guild.name %>" class="guild-icon mb-2">
                    <% } else { %>
                        <div class="guild-icon mx-auto mb-2 bg-secondary d-flex align-items-center justify-content-center text-white fw-bold">
                            <%= guild.name.charAt(0).toUpperCase() %>
                        </div>
                    <% } %>
                    <h6 class="mb-1"><%= guild.name %></h6>
                    <small class="text-muted">Server Management</small>
                </div>
                
                <h6 class="text-muted text-uppercase fw-bold mb-3">Management</h6>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#overview">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Overview
                    </a>
                    <a class="nav-link" href="#moderation">
                        <i class="fas fa-shield-alt me-2"></i>
                        Moderation
                    </a>
                    <a class="nav-link" href="#members">
                        <i class="fas fa-users me-2"></i>
                        Members
                    </a>
                    <a class="nav-link" href="#channels">
                        <i class="fas fa-hashtag me-2"></i>
                        Channels
                    </a>
                    <a class="nav-link" href="#roles">
                        <i class="fas fa-user-tag me-2"></i>
                        Roles
                    </a>
                </nav>
                
                <hr class="my-4">
                
                <h6 class="text-muted text-uppercase fw-bold mb-3">Configuration</h6>
                <nav class="nav flex-column">
                    <a class="nav-link" href="#settings">
                        <i class="fas fa-cog me-2"></i>
                        Settings
                    </a>
                    <a class="nav-link" href="#automod">
                        <i class="fas fa-robot me-2"></i>
                        AutoMod
                    </a>
                    <a class="nav-link" href="#welcome">
                        <i class="fas fa-hand-wave me-2"></i>
                        Welcome
                    </a>
                    <a class="nav-link" href="#logs">
                        <i class="fas fa-file-alt me-2"></i>
                        Logs
                    </a>
                </nav>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <a href="/dashboard" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 fw-bold"><%= guild.name %> Management</h1>
                    <p class="text-muted">Configure and monitor your Discord server</p>
                </div>
                <div>
                    <span class="status-badge status-online me-2">
                        <i class="fas fa-circle me-1"></i>
                        Bot Online
                    </span>
                    <button class="btn btn-discord btn-sm" onclick="runProfessionalSetup()">
                        <i class="fas fa-magic me-1"></i>
                        Professional Setup
                    </button>
                </div>
            </div>
            
            <!-- Server Stats -->
            <div class="row g-4 mb-5">
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number" id="memberCount">-</div>
                            <div class="stats-label">Total Members</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number" id="onlineCount">-</div>
                            <div class="stats-label">Online Now</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number" id="verifiedCount">-</div>
                            <div class="stats-label">Verified Today</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card card">
                        <div class="card-body text-center">
                            <div class="stats-number" id="moderationCount">-</div>
                            <div class="stats-label">Mod Actions</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content Tabs -->
            <div class="row">
                <!-- Overview Tab -->
                <div class="col-12" id="overview-tab">
                    <div class="row g-4">
                        <!-- Server Analytics -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        Server Analytics
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="memberChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-bolt me-2"></i>
                                        Quick Actions
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary" onclick="runProfessionalSetup()">
                                            <i class="fas fa-magic me-2"></i>
                                            Professional Setup
                                        </button>
                                        <button class="btn btn-outline-success" onclick="testVerification()">
                                            <i class="fas fa-check-circle me-2"></i>
                                            Test Verification
                                        </button>
                                        <button class="btn btn-outline-info" onclick="viewLogs()">
                                            <i class="fas fa-file-alt me-2"></i>
                                            View Logs
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="exportData()">
                                            <i class="fas fa-download me-2"></i>
                                            Export Data
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Server Health -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-heartbeat me-2"></i>
                                        Server Health
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <span class="small">Bot Performance</span>
                                            <span class="small text-success">Excellent</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: 95%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <span class="small">Setup Completion</span>
                                            <span class="small text-info">85%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-info" style="width: 85%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <span class="small">Security Score</span>
                                            <span class="small text-warning">Good</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-warning" style="width: 75%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center mt-3">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-sync me-1"></i>
                                            Refresh Status
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-clock me-2"></i>
                                        Recent Activity
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex align-items-center">
                                            <div class="feature-icon me-3" style="width: 32px; height: 32px; font-size: 1rem;">
                                                <i class="fas fa-user-plus"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold">New member joined</div>
                                                <small class="text-muted">User#1234 joined the server</small>
                                            </div>
                                            <small class="text-muted">2 minutes ago</small>
                                        </div>
                                        
                                        <div class="list-group-item d-flex align-items-center">
                                            <div class="feature-icon me-3" style="width: 32px; height: 32px; font-size: 1rem;">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold">Member verified</div>
                                                <small class="text-muted">User#5678 completed verification</small>
                                            </div>
                                            <small class="text-muted">5 minutes ago</small>
                                        </div>
                                        
                                        <div class="list-group-item d-flex align-items-center">
                                            <div class="feature-icon me-3" style="width: 32px; height: 32px; font-size: 1rem;">
                                                <i class="fas fa-cog"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold">Settings updated</div>
                                                <small class="text-muted">Welcome message configuration changed</small>
                                            </div>
                                            <small class="text-muted">1 hour ago</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Load server stats
    async function loadServerStats() {
        try {
            const response = await fetch(`/api/server/<%= guild.id %>/stats`);
            const stats = await response.json();
            
            // Update member chart
            updateMemberChart(stats);
            
        } catch (error) {
            console.error('Error loading server stats:', error);
        }
    }
    
    // Update member chart
    function updateMemberChart(stats) {
        const ctx = document.getElementById('memberChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: stats.memberJoins?.map(d => d.date) || [],
                datasets: [{
                    label: 'New Members',
                    data: stats.memberJoins?.map(d => d.joins) || [],
                    borderColor: '#5865F2',
                    backgroundColor: 'rgba(88, 101, 242, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Verifications',
                    data: stats.verifications?.map(d => d.verifications) || [],
                    borderColor: '#57F287',
                    backgroundColor: 'rgba(87, 242, 135, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Quick action functions
    function runProfessionalSetup() {
        alert('Professional setup would be triggered here via Discord bot commands');
    }
    
    function testVerification() {
        alert('Verification test would be performed here');
    }
    
    function viewLogs() {
        alert('Logs viewer would open here');
    }
    
    function exportData() {
        alert('Data export would start here');
    }
    
    // Load data when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadServerStats();
    });
</script>
