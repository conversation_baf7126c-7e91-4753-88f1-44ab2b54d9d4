const { ChannelType, PermissionFlagsBits } = require('discord.js');
const { logger } = require('./logger');

// Professional server layout configuration
const SERVER_LAYOUT = {
    roles: [
        { name: '👑 Server Owner', color: '#ff0000', permissions: [], position: 20 },
        { name: '🛡️ Administrator', color: '#ff6600', permissions: ['Administrator'], position: 19 },
        { name: '🔱 Head Moderator', color: '#ff9900', permissions: ['ManageGuild', 'ManageRoles', 'ManageChannels', 'KickMembers', 'BanMembers', 'ModerateMembers', 'ManageMessages'], position: 18 },
        { name: '⚔️ Senior Moderator', color: '#ffcc00', permissions: ['ManageRoles', 'KickMembers', 'BanMembers', 'ModerateMembers', 'ManageMessages'], position: 17 },
        { name: '🛡️ Moderator', color: '#ffff00', permissions: ['KickMembers', 'ModerateMembers', 'ManageMessages'], position: 16 },
        { name: '🔰 Trial Moderator', color: '#ccff00', permissions: ['ModerateMembers', 'ManageMessages'], position: 15 },
        { name: '🎯 Support Lead', color: '#99ff00', permissions: ['ManageMessages'], position: 14 },
        { name: '🎫 Support Staff', color: '#66ff00', permissions: [], position: 13 },
        { name: '💎 VIP/Donor', color: '#33ff00', permissions: [], position: 12 },
        { name: '🌟 Active Member', color: '#00ff33', permissions: [], position: 11 },
        { name: '✅ Verified Member', color: '#00ff66', permissions: [], position: 10 },
        { name: '👶 New Member', color: '#00ff99', permissions: [], position: 9 }
    ],
    
    categories: [
        {
            name: '📋 INFORMATION',
            channels: [
                { name: '📜-rules', description: '📜 Server Rules & Guidelines | Read before participating | Violations result in warnings/bans' },
                { name: '📢-announcements', description: '📢 Official Server Announcements | Important updates and news | Enable notifications 🔔' },
                { name: '👋-welcome', description: '👋 Welcome to our community! | New member introductions | Check #📜rules first' },
                { name: '❓-faq', description: '❓ Frequently Asked Questions | Search before asking | Common solutions here' },
                { name: '📰-updates', description: '📰 Bot & Server Updates | ModuBot changelog | Technical announcements' },
                { name: '🔗-useful-links', description: '🔗 Useful Links & Resources | External tools | Official websites | Documentation' },
                { name: '📊-server-stats', description: '📊 Live Server Statistics | Member count | Growth metrics | Server insights' }
            ],
            permissions: {
                '@everyone': { view: true, send: false, react: false },
                'staff': { view: true, send: true, react: true, manage: true }
            }
        },
        {
            name: '💬 GENERAL',
            channels: [
                { name: '💬-general-chat', description: '💬 Main Community Chat | General discussions | Keep it friendly and on-topic' },
                { name: '🎭-introductions', description: '🎭 Introduce Yourself! | New members welcome | Tell us about yourself | One intro per person' },
                { name: '🗣️-off-topic', description: '🗣️ Off-Topic Chat | Casual conversations | Random discussions | Keep it appropriate' },
                { name: '😂-memes-and-fun', description: '😂 Memes & Fun | Share memes and jokes | Keep it clean | No spam posting' },
                { name: '🎨-media-sharing', description: '🎨 Media Sharing | Share your art, photos, screenshots | Credit original creators | No NSFW' },
                { name: '🤖-bot-commands', description: '🤖 Bot Commands | Test bot commands here | Keep other channels clean | All bots allowed' },
                { name: '🎵-music-commands', description: '🎵 Music Commands | Music bot controls | Queue requests | Now playing info' }
            ],
            permissions: {
                '@everyone': { view: false },
                'New Member': { view: true, send: false, react: false },
                'Verified Member+': { view: true, send: true, react: true, threads: true },
                'staff': { view: true, send: true, react: true, manage: true, threads: true }
            }
        },
        {
            name: '🎫 SUPPORT',
            channels: [
                { name: '🎫-support-info', description: '🎫 Support Information | How to get help | Use /ticket to create a private support channel | Check #❓faq first' },
                { name: '📋-ticket-logs', description: '📋 Ticket Logs | Closed ticket transcripts | Staff-only channel | Support analytics and history' },
                { name: '🐛-bug-reports', description: '🐛 Bug Reports | Report bugs and issues | Use the template | Include screenshots/logs | Staff will investigate' },
                { name: '💡-feature-requests', description: '💡 Feature Requests | Suggest new features | Community voting with reactions | Detailed descriptions please' },
                { name: '📚-knowledge-base', description: '📚 Knowledge Base | Self-help articles | Common solutions | Search before asking | Updated regularly' }
            ],
            permissions: {
                '@everyone': { view: false },
                'New Member': { view: true, send: false, react: false },
                'Verified Member+': { view: true, send: true, react: true, threads: true },
                'Support Staff': { view: true, send: true, react: true, manage: true, threads: true },
                'staff': { view: true, send: true, react: true, manage: true, threads: true }
            }
        },
        {
            name: '🏆 COMMUNITY',
            channels: [
                { name: '💡-suggestions', description: '💡 Server Suggestions | Community improvement ideas | Voting with reactions | Staff will review' },
                { name: '📊-polls', description: '📊 Community Polls | Vote on server decisions | Results help shape our community' },
                { name: '📅-events', description: '📅 Community Events | Upcoming events and activities | Event announcements and coordination' },
                { name: '🎉-giveaways', description: '🎉 Giveaways & Contests | Participate in giveaways | Contest announcements and winners' },
                { name: '🏅-achievements', description: '🏅 Member Achievements | Celebrate milestones | Member highlights and recognition' },
                { name: '📈-leaderboard', description: '📈 XP Leaderboard | Top contributors | Level rankings and statistics' }
            ],
            permissions: {
                '@everyone': { view: false },
                'Verified Member+': { view: true, send: true, react: true, threads: true },
                'staff': { view: true, send: true, react: true, manage: true, threads: true }
            }
        },
        {
            name: '🔒 STAFF',
            channels: [
                { name: '👥-staff-chat', description: '👥 Staff Discussion | Private staff conversations | Coordination and planning' },
                { name: '📋-mod-logs', description: '📋 Moderation Logs | ModuBot action logs | Automatic moderation tracking' },
                { name: '📢-staff-announcements', description: '📢 Staff Announcements | Important staff updates | Policy changes and notices' },
                { name: '🎫-ticket-management', description: '🎫 Ticket Management | Ticket oversight | Support coordination and escalation' },
                { name: '🔧-bot-management', description: '🔧 Bot Management | Bot configuration | Testing and troubleshooting' },
                { name: '📊-analytics', description: '📊 Server Analytics | Growth metrics | Performance insights and reports' }
            ],
            permissions: {
                '@everyone': { view: false },
                'staff': { view: true, send: true, react: true, manage: true, threads: true }
            }
        }
    ]
};

// Voice channels configuration
const VOICE_CHANNELS = [
    { name: '🎤 General Voice 1', category: 'VOICE CHANNELS' },
    { name: '🎤 General Voice 2', category: 'VOICE CHANNELS' },
    { name: '🎮 Gaming Voice 1', category: 'VOICE CHANNELS' },
    { name: '🎮 Gaming Voice 2', category: 'VOICE CHANNELS' },
    { name: '🔒 Staff Voice', category: 'VOICE CHANNELS' },
    { name: '🎫 Support Voice', category: 'VOICE CHANNELS' }
];

/**
 * Create professional server layout
 * @param {Guild} guild - Discord guild
 * @param {Function} progressCallback - Progress update callback
 * @returns {Object} Setup results
 */
async function createProfessionalLayout(guild, progressCallback = () => {}) {
    const results = {
        rolesCreated: [],
        categoriesCreated: [],
        channelsCreated: [],
        errors: []
    };

    try {
        progressCallback('🎭 Creating role hierarchy...');
        
        // Create roles
        for (const roleConfig of SERVER_LAYOUT.roles) {
            try {
                // Check if role already exists
                const existingRole = guild.roles.cache.find(role => 
                    role.name === roleConfig.name || 
                    role.name.includes(roleConfig.name.replace(/[^\w\s]/g, ''))
                );

                if (!existingRole) {
                    const permissions = roleConfig.permissions.map(perm => PermissionFlagsBits[perm]).filter(Boolean);
                    
                    const role = await guild.roles.create({
                        name: roleConfig.name,
                        color: roleConfig.color,
                        permissions: permissions,
                        position: roleConfig.position,
                        mentionable: false
                    });
                    
                    results.rolesCreated.push(role.name);
                    logger.info(`Created role: ${role.name}`);
                } else {
                    logger.info(`Role already exists: ${roleConfig.name}`);
                }
            } catch (error) {
                logger.error(`Error creating role ${roleConfig.name}:`, error);
                results.errors.push(`Role ${roleConfig.name}: ${error.message}`);
            }
        }

        progressCallback('📁 Creating categories and channels...');

        // Create categories and channels
        for (const categoryConfig of SERVER_LAYOUT.categories) {
            try {
                // Check if category already exists
                let category = guild.channels.cache.find(channel => 
                    channel.type === ChannelType.GuildCategory && 
                    (channel.name === categoryConfig.name || 
                     channel.name.includes(categoryConfig.name.replace(/[^\w\s]/g, '')))
                );

                if (!category) {
                    category = await guild.channels.create({
                        name: categoryConfig.name,
                        type: ChannelType.GuildCategory,
                        permissionOverwrites: await buildCategoryPermissions(guild, categoryConfig.permissions)
                    });
                    
                    results.categoriesCreated.push(category.name);
                    logger.info(`Created category: ${category.name}`);
                } else {
                    // Update permissions for existing category
                    await updateCategoryPermissions(category, categoryConfig.permissions, guild);
                    logger.info(`Updated permissions for existing category: ${category.name}`);
                }

                // Create channels in category
                for (const channelConfig of categoryConfig.channels) {
                    try {
                        // Check if channel already exists
                        const existingChannel = guild.channels.cache.find(channel => 
                            channel.name === channelConfig.name || 
                            channel.name.includes(channelConfig.name.replace(/[^\w\s-]/g, ''))
                        );

                        if (!existingChannel) {
                            const channel = await guild.channels.create({
                                name: channelConfig.name,
                                type: ChannelType.GuildText,
                                parent: category.id,
                                topic: channelConfig.description
                            });
                            
                            results.channelsCreated.push(channel.name);
                            logger.info(`Created channel: ${channel.name}`);
                        } else {
                            // Update topic for existing channel
                            if (existingChannel.topic !== channelConfig.description) {
                                await existingChannel.setTopic(channelConfig.description);
                                logger.info(`Updated topic for existing channel: ${existingChannel.name}`);
                            }
                        }
                    } catch (error) {
                        logger.error(`Error creating channel ${channelConfig.name}:`, error);
                        results.errors.push(`Channel ${channelConfig.name}: ${error.message}`);
                    }
                }
            } catch (error) {
                logger.error(`Error creating category ${categoryConfig.name}:`, error);
                results.errors.push(`Category ${categoryConfig.name}: ${error.message}`);
            }
        }

        progressCallback('🔊 Creating voice channels...');

        // Create voice channels category if it doesn't exist
        let voiceCategory = guild.channels.cache.find(channel => 
            channel.type === ChannelType.GuildCategory && 
            channel.name.includes('VOICE')
        );

        if (!voiceCategory) {
            voiceCategory = await guild.channels.create({
                name: '🔊 VOICE CHANNELS',
                type: ChannelType.GuildCategory
            });
            results.categoriesCreated.push(voiceCategory.name);
        }

        // Create voice channels
        for (const voiceConfig of VOICE_CHANNELS) {
            try {
                const existingVoice = guild.channels.cache.find(channel => 
                    channel.type === ChannelType.GuildVoice && 
                    channel.name.includes(voiceConfig.name.replace(/[^\w\s]/g, ''))
                );

                if (!existingVoice) {
                    const voiceChannel = await guild.channels.create({
                        name: voiceConfig.name,
                        type: ChannelType.GuildVoice,
                        parent: voiceCategory.id
                    });
                    
                    results.channelsCreated.push(voiceChannel.name);
                    logger.info(`Created voice channel: ${voiceChannel.name}`);
                }
            } catch (error) {
                logger.error(`Error creating voice channel ${voiceConfig.name}:`, error);
                results.errors.push(`Voice channel ${voiceConfig.name}: ${error.message}`);
            }
        }

        progressCallback('✅ Professional layout setup complete!');
        
    } catch (error) {
        logger.error('Error in createProfessionalLayout:', error);
        results.errors.push(`General error: ${error.message}`);
    }

    return results;
}

/**
 * Build permission overwrites for category
 */
async function buildCategoryPermissions(guild, permissionConfig) {
    const overwrites = [];
    
    // Default @everyone permissions
    overwrites.push({
        id: guild.roles.everyone.id,
        deny: [PermissionFlagsBits.SendMessages, PermissionFlagsBits.CreatePublicThreads, PermissionFlagsBits.CreatePrivateThreads]
    });

    // Add specific role permissions based on config
    for (const [roleName, permissions] of Object.entries(permissionConfig)) {
        if (roleName === '@everyone') {
            // Update @everyone permissions
            const everyoneOverwrite = overwrites.find(o => o.id === guild.roles.everyone.id);
            if (permissions.view === false) {
                everyoneOverwrite.deny.push(PermissionFlagsBits.ViewChannel);
            }
            if (permissions.send === true) {
                everyoneOverwrite.allow = everyoneOverwrite.allow || [];
                everyoneOverwrite.allow.push(PermissionFlagsBits.SendMessages);
            }
        } else {
            // Find role and add permissions
            const role = guild.roles.cache.find(r => r.name.includes(roleName) || roleName.includes('staff'));
            if (role) {
                const allow = [];
                const deny = [];
                
                if (permissions.view) allow.push(PermissionFlagsBits.ViewChannel);
                if (permissions.send) allow.push(PermissionFlagsBits.SendMessages);
                if (permissions.react) allow.push(PermissionFlagsBits.AddReactions);
                if (permissions.manage) allow.push(PermissionFlagsBits.ManageMessages);
                if (permissions.threads) allow.push(PermissionFlagsBits.CreatePublicThreads);
                
                overwrites.push({ id: role.id, allow, deny });
            }
        }
    }
    
    return overwrites;
}

/**
 * Update permissions for existing category
 */
async function updateCategoryPermissions(category, permissionConfig, guild) {
    try {
        const overwrites = await buildCategoryPermissions(guild, permissionConfig);
        await category.permissionOverwrites.set(overwrites);
        logger.info(`Updated permissions for category: ${category.name}`);
    } catch (error) {
        logger.error(`Error updating category permissions for ${category.name}:`, error);
    }
}

module.exports = {
    createProfessionalLayout,
    SERVER_LAYOUT,
    VOICE_CHANNELS
};
