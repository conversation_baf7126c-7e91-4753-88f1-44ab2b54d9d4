/* ModuBot Dashboard Custom Styles */

/* Animation Utilities */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Page Animations */
.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in {
    animation: slideInLeft 0.5s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Interactive Elements */
.interactive {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Status Indicators */
.status-indicator {
    position: relative;
    display: inline-block;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.online::before {
    background: var(--success-green);
    animation: pulse 2s infinite;
}

.status-indicator.offline::before {
    background: var(--error-red);
}

.status-indicator.idle::before {
    background: var(--warning-yellow);
}

/* Card Enhancements */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Button Enhancements */
.btn-glow {
    position: relative;
    overflow: hidden;
}

.btn-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-glow:hover::before {
    left: 100%;
}

/* Progress Bars */
.progress-animated .progress-bar {
    animation: progress-animation 2s ease-in-out;
}

@keyframes progress-animation {
    0% {
        width: 0%;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-green);
}

.notification.error {
    border-left: 4px solid var(--error-red);
}

.notification.warning {
    border-left: 4px solid var(--warning-yellow);
}

.notification.info {
    border-left: 4px solid var(--discord-blurple);
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem !important;
    }
    
    .feature-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 1.2rem !important;
    }
    
    .guild-icon {
        width: 48px !important;
        height: 48px !important;
    }
    
    .card-body {
        padding: 1rem !important;
    }
    
    .main-content {
        padding: 1rem !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .card {
        background: rgba(44, 47, 51, 0.95) !important;
        color: #dcddde;
    }
    
    .navbar {
        background: rgba(44, 47, 51, 0.95) !important;
    }
    
    .sidebar {
        background: rgba(44, 47, 51, 0.95) !important;
    }
    
    .text-muted {
        color: #72767d !important;
    }
}

/* Utility Classes */
.text-discord {
    color: var(--discord-blurple) !important;
}

.bg-discord {
    background-color: var(--discord-blurple) !important;
}

.border-discord {
    border-color: var(--discord-blurple) !important;
}

.shadow-discord {
    box-shadow: 0 4px 12px rgba(88, 101, 242, 0.3) !important;
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--discord-blurple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip Enhancements */
.tooltip-inner {
    background-color: var(--discord-dark);
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 0.875rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--discord-dark);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--discord-dark);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--discord-dark);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--discord-dark);
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .footer,
    .btn,
    .card-footer {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
